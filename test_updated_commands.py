"""
Test script to demonstrate the updated command implementations:
1. /end command - disconnects chat and sends exact message
2. /search command - uses same matching logic as registration
3. /help command - lists all commands in clear format
4. Independent and clean command structure
"""

# Simulate the bot's data structures
users_data = {}
active_chats = {}

def register_user(user_id, nickname, age, gender, looking_for, language):
    """Register a user with exact bot structure"""
    users_data[user_id] = {
        "nickname": nickname,
        "age": age,
        "gender": gender,
        "looking_for": looking_for,
        "language": language
    }
    print(f"✅ {nickname} registered successfully!")

def find_match(user_id):
    """Find a compatible match using the exact algorithm from the bot"""
    if user_id not in users_data:
        return None
    
    current_user = users_data[user_id]
    
    for potential_match_id, potential_match in users_data.items():
        # Skip self
        if potential_match_id == user_id:
            continue
            
        # Skip if either user is already chatting
        if user_id in active_chats or potential_match_id in active_chats:
            continue
            
        # Check if preferences match (mutual compatibility)
        if (current_user["looking_for"] == potential_match["gender"] and 
            potential_match["looking_for"] == current_user["gender"]):
            return potential_match_id
    
    return None

def create_match(user1_id, user2_id):
    """Create a match between two users"""
    active_chats[user1_id] = user2_id
    active_chats[user2_id] = user1_id
    
    user1_name = users_data[user1_id]["nickname"]
    user2_name = users_data[user2_id]["nickname"]
    print(f"🎉 MATCH CREATED: {user1_name} ↔ {user2_name}")
    print(f"   Both users receive: 'You are now connected anonymously. Say hi!'")

def simulate_end_command(user_id):
    """Simulate the updated /end command"""
    print(f"\n🔚 {users_data[user_id]['nickname']} sends /end command")
    
    if user_id not in active_chats:
        print("❌ Response: You're not currently in a chat.")
        return
    
    partner_id = active_chats[user_id]
    user_name = users_data[user_id]["nickname"]
    partner_name = users_data[partner_id]["nickname"]
    
    # Remove both users from active chats
    active_chats.pop(user_id, None)
    active_chats.pop(partner_id, None)
    
    # Send the exact message as requested (no additional search prompt)
    end_message = "❌ Your chat has ended. The other person has left the conversation."
    
    print(f"📤 Message to {user_name}: {end_message}")
    print(f"📤 Message to {partner_name}: {end_message}")
    
    print(f"✅ Chat ended between {user_name} and {partner_name}")

def simulate_search_command(user_id):
    """Simulate the updated /search command"""
    print(f"\n🔍 {users_data[user_id]['nickname']} sends /search command")
    
    if user_id in active_chats:
        print("❌ Response: You're already in a chat. Use /end to leave your current chat first.")
        return
    
    # Try to find a match using the same logic as after registration
    match_id = find_match(user_id)
    if match_id:
        create_match(user_id, match_id)
    else:
        print("📤 Response: 🔍 No matches found right now. We'll notify you when someone compatible joins!")

def simulate_help_command(user_id):
    """Simulate the /help command"""
    print(f"\n❓ {users_data[user_id]['nickname']} sends /help command")
    
    help_text = (
        "📋 Available Commands:\n\n"
        "/start – Register your profile\n"
        "/help – Show available commands\n"
        "/search – Search for a new match\n"
        "/end – Leave current chat"
    )
    
    print(f"📤 Response:\n{help_text}")

def demonstrate_updated_commands():
    print("🎯 FlirtLine Bot - Updated Commands Demonstration")
    print("=" * 60)
    
    # Setup: Register users
    print("\n📝 SETUP: Registering Users")
    print("-" * 30)
    register_user(101, "Alice", 25, "Female", "Male", "English")
    register_user(102, "Bob", 28, "Male", "Female", "English")
    register_user(103, "Charlie", 22, "Male", "Male", "English")
    
    # Test 1: /help command
    print("\n🎯 TEST 1: /help Command")
    print("-" * 30)
    simulate_help_command(101)  # Alice asks for help
    
    # Test 2: /search command (no matches initially)
    print("\n🎯 TEST 2: /search Command (No Current Matches)")
    print("-" * 50)
    simulate_search_command(103)  # Charlie searches (no compatible users)
    
    # Test 3: /search command (successful match)
    print("\n🎯 TEST 3: /search Command (Successful Match)")
    print("-" * 45)
    simulate_search_command(101)  # Alice searches and should match with Bob
    
    print(f"\nActive chats after successful search: {dict(active_chats)}")
    
    # Test 4: /search command while in chat
    print("\n🎯 TEST 4: /search Command While In Chat")
    print("-" * 40)
    simulate_search_command(102)  # Bob tries to search while already matched
    
    # Test 5: /end command
    print("\n🎯 TEST 5: /end Command")
    print("-" * 25)
    simulate_end_command(101)  # Alice ends the chat
    
    print(f"\nActive chats after /end: {dict(active_chats)}")
    
    # Test 6: /end command when not in chat
    print("\n🎯 TEST 6: /end Command When Not In Chat")
    print("-" * 40)
    simulate_end_command(103)  # Charlie tries to end non-existent chat
    
    # Test 7: Multiple searches after chat ended
    print("\n🎯 TEST 7: Multiple Searches After Chat Ended")
    print("-" * 45)
    simulate_search_command(101)  # Alice searches again
    simulate_search_command(102)  # Bob searches again
    
    # Final status
    print(f"\n📊 FINAL STATUS")
    print("-" * 20)
    print(f"Total users: {len(users_data)}")
    print(f"Active chats: {len(active_chats) // 2}")
    print(f"Active chats structure: {dict(active_chats)}")
    
    print(f"\n✨ UPDATED COMMANDS DEMONSTRATION COMPLETE!")
    print(f"\n🎯 Commands Demonstrated:")
    print(f"✅ /end - Disconnects chat and sends exact message:")
    print(f"   • '❌ Your chat has ended. The other person has left the conversation.'")
    print(f"   • Removes both users from active_chats dictionary")
    print(f"   • Clean and independent implementation")
    print(f"✅ /search - Uses same matching logic as registration:")
    print(f"   • Finds compatible users based on mutual preferences")
    print(f"   • Exact message: '🔍 No matches found right now. We'll notify you when someone compatible joins!'")
    print(f"   • Prevents searching while in active chat")
    print(f"✅ /help - Lists all commands in clear format:")
    print(f"   • /start – Register your profile")
    print(f"   • /help – Show available commands")
    print(f"   • /search – Search for a new match")
    print(f"   • /end – Leave current chat")
    print(f"✅ All commands are independent and easily editable")

if __name__ == "__main__":
    demonstrate_updated_commands()
