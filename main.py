import asyncio
import logging
import os
from dotenv import load_dotenv
from aiogram import <PERSON><PERSON>, Di<PERSON>atch<PERSON>, types, F
from aiogram.filters import <PERSON>Start, StateFilter
from aiogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.fsm.storage.memory import MemoryStorage

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)

# Bot token from environment variable
BOT_TOKEN = os.getenv('BOT_TOKEN')

# User data storage (in production, use a database)
users_data = {}

# Active chats storage - stores current matches
active_chats = {}

# States for registration process
class RegistrationStates(StatesGroup):
    waiting_for_nickname = State()
    waiting_for_age = State()
    waiting_for_gender = State()
    waiting_for_preferred_gender = State()
    waiting_for_language = State()

# Initialize bot and dispatcher with memory storage for FSM
storage = MemoryStorage()
bot = Bot(token=BOT_TOKEN)
dp = Dispatcher(storage=storage)


# Matching system functions
async def find_match(user_id):
    """
    Find a compatible match for the given user
    Returns the matched user_id if found, None otherwise
    """
    if user_id not in users_data:
        return None

    current_user = users_data[user_id]

    # Look for available users (not currently chatting)
    for potential_match_id, potential_match in users_data.items():
        # Skip self
        if potential_match_id == user_id:
            continue

        # Skip if either user is already chatting
        if user_id in active_chats or potential_match_id in active_chats:
            continue

        # Check if preferences match
        if (current_user["looking_for"] == potential_match["gender"] and
            potential_match["looking_for"] == current_user["gender"]):
            return potential_match_id

    return None


async def create_match(user1_id, user2_id):
    """
    Create a match between two users and notify them
    """
    # Store the match in active_chats
    active_chats[user1_id] = user2_id
    active_chats[user2_id] = user1_id

    # Send notification to both users
    match_message = "🎉 You are now connected anonymously. Say hi!"

    try:
        await bot.send_message(user1_id, match_message)
        await bot.send_message(user2_id, match_message)
        logging.info(f"Match created between {user1_id} and {user2_id}")
    except Exception as e:
        logging.error(f"Error sending match notification: {e}")
        # Remove the match if notification failed
        active_chats.pop(user1_id, None)
        active_chats.pop(user2_id, None)


@dp.message(CommandStart())
async def command_start_handler(message: Message, state: FSMContext) -> None:
    """
    This handler receives messages with `/start` command and starts registration
    """
    user_id = message.from_user.id

    # Check if user is already registered
    if user_id in users_data:
        await message.answer(
            f"👋 Welcome back, {users_data[user_id]['nickname']}!\n\n"
            "You're already registered. Your profile:\n"
            f"📝 Nickname: {users_data[user_id]['nickname']}\n"
            f"🎂 Age: {users_data[user_id]['age']}\n"
            f"👤 Gender: {users_data[user_id]['gender']}\n"
            f"💕 Looking for: {users_data[user_id]['looking_for']}\n"
            f"🌍 Language: {users_data[user_id]['language']}"
        )
        return

    welcome_text = (
        f"👋 Hello, {message.from_user.full_name}!\n\n"
        "Welcome to FlirtLine Bot! 💕\n"
        "I'm here to help you with your conversations.\n\n"
        "Let's set up your profile step by step.\n"
        "First, please tell me your nickname (not your Telegram username):"
    )

    await message.answer(welcome_text)
    await state.set_state(RegistrationStates.waiting_for_nickname)


# Registration step handlers
@dp.message(StateFilter(RegistrationStates.waiting_for_nickname))
async def process_nickname(message: Message, state: FSMContext) -> None:
    """Handle nickname input"""
    nickname = message.text.strip()

    if len(nickname) < 2 or len(nickname) > 20:
        await message.answer("Please enter a nickname between 2 and 20 characters:")
        return

    await state.update_data(nickname=nickname)
    await message.answer(f"Great! Nice to meet you, {nickname}! 😊\n\nNow, please tell me your age:")
    await state.set_state(RegistrationStates.waiting_for_age)


@dp.message(StateFilter(RegistrationStates.waiting_for_age))
async def process_age(message: Message, state: FSMContext) -> None:
    """Handle age input"""
    try:
        age = int(message.text.strip())
        if age < 18 or age > 100:
            await message.answer("Please enter a valid age between 18 and 100:")
            return
    except ValueError:
        await message.answer("Please enter a valid number for your age:")
        return

    await state.update_data(age=age)

    # Create gender selection keyboard
    gender_keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="👨 Male", callback_data="gender_male")],
        [InlineKeyboardButton(text="👩 Female", callback_data="gender_female")]
    ])

    await message.answer("Perfect! Now, please select your gender:", reply_markup=gender_keyboard)
    await state.set_state(RegistrationStates.waiting_for_gender)


@dp.callback_query(StateFilter(RegistrationStates.waiting_for_gender), F.data.startswith("gender_"))
async def process_gender(callback: CallbackQuery, state: FSMContext) -> None:
    """Handle gender selection"""
    gender = "Male" if callback.data == "gender_male" else "Female"
    await state.update_data(gender=gender)

    # Create preferred gender selection keyboard
    preferred_keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="👨 Male", callback_data="preferred_male")],
        [InlineKeyboardButton(text="👩 Female", callback_data="preferred_female")]
    ])

    await callback.message.edit_text(
        f"Got it! You selected: {gender} 👍\n\n"
        "Now, what gender would you prefer to chat with?",
        reply_markup=preferred_keyboard
    )
    await state.set_state(RegistrationStates.waiting_for_preferred_gender)
    await callback.answer()


@dp.callback_query(StateFilter(RegistrationStates.waiting_for_preferred_gender), F.data.startswith("preferred_"))
async def process_preferred_gender(callback: CallbackQuery, state: FSMContext) -> None:
    """Handle preferred gender selection"""
    looking_for = "Male" if callback.data == "preferred_male" else "Female"
    await state.update_data(looking_for=looking_for)

    # Create language selection keyboard
    language_keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="🇺🇸 English", callback_data="lang_english")],
        [InlineKeyboardButton(text="🇪🇸 Spanish", callback_data="lang_spanish")],
        [InlineKeyboardButton(text="🇫🇷 French", callback_data="lang_french")],
        [InlineKeyboardButton(text="🇩🇪 German", callback_data="lang_german")],
        [InlineKeyboardButton(text="🇮🇹 Italian", callback_data="lang_italian")]
    ])

    await callback.message.edit_text(
        f"Excellent! You prefer to chat with: {looking_for} 💕\n\n"
        "Finally, please select your preferred language:",
        reply_markup=language_keyboard
    )
    await state.set_state(RegistrationStates.waiting_for_language)
    await callback.answer()


@dp.callback_query(StateFilter(RegistrationStates.waiting_for_language), F.data.startswith("lang_"))
async def process_language(callback: CallbackQuery, state: FSMContext) -> None:
    """Handle language selection and complete registration"""
    language_map = {
        "lang_english": "English",
        "lang_spanish": "Spanish",
        "lang_french": "French",
        "lang_german": "German",
        "lang_italian": "Italian"
    }

    language = language_map.get(callback.data, "English")
    await state.update_data(language=language)

    # Get all collected data
    data = await state.get_data()
    user_id = callback.from_user.id

    # Save to users_data dictionary
    users_data[user_id] = {
        "nickname": data['nickname'],
        "age": data['age'],
        "gender": data['gender'],
        "looking_for": data['looking_for'],
        "language": data['language']
    }

    # Send completion message
    completion_text = (
        f"🎉 Registration complete! Welcome to FlirtLine, {data['nickname']}!\n\n"
        "📋 Your profile summary:\n"
        f"📝 Nickname: {data['nickname']}\n"
        f"🎂 Age: {data['age']}\n"
        f"👤 Gender: {data['gender']}\n"
        f"💕 Looking for: {data['looking_for']}\n"
        f"🌍 Language: {language}\n\n"
        "You're all set! Looking for a match... 🔍"
    )

    await callback.message.edit_text(completion_text)
    await state.clear()
    await callback.answer("Registration completed! 🎉")

    # Try to find a match
    match_id = await find_match(user_id)
    if match_id:
        await create_match(user_id, match_id)
    else:
        await bot.send_message(
            user_id,
            "🔍 No matches found right now. We'll notify you when someone compatible joins! 💕"
        )


@dp.message()
async def message_handler(message: Message) -> None:
    """
    Handle messages from users - forward to matched partner or provide guidance
    """
    user_id = message.from_user.id

    # Check if user is registered
    if user_id not in users_data:
        await message.answer(
            "Please start by typing /start to register your profile first! 😊"
        )
        return

    # Check if user is in an active chat
    if user_id in active_chats:
        partner_id = active_chats[user_id]

        # Forward the message to the partner
        try:
            if message.text:
                await bot.send_message(partner_id, message.text)
            elif message.photo:
                await bot.send_photo(partner_id, message.photo[-1].file_id, caption=message.caption)
            elif message.voice:
                await bot.send_voice(partner_id, message.voice.file_id)
            elif message.video:
                await bot.send_video(partner_id, message.video.file_id, caption=message.caption)
            elif message.document:
                await bot.send_document(partner_id, message.document.file_id, caption=message.caption)
            elif message.sticker:
                await bot.send_sticker(partner_id, message.sticker.file_id)
            else:
                # For other message types, try to copy the message
                await message.copy_to(partner_id)

        except Exception as e:
            logging.error(f"Error forwarding message from {user_id} to {partner_id}: {e}")
            await message.answer(
                "❌ Sorry, there was an error sending your message. Your partner might have left the chat."
            )
    else:
        # User is not in a chat, try to find a match
        match_id = await find_match(user_id)
        if match_id:
            await create_match(user_id, match_id)
        else:
            await message.answer(
                f"Hello {users_data[user_id]['nickname']}! 👋\n\n"
                "🔍 You're not currently matched with anyone. Looking for a compatible partner...\n\n"
                "💡 Tip: More users joining increases your chances of finding a match! 💕"
            )


# Chat management commands
@dp.message(F.text == "/end")
async def end_chat_command(message: Message) -> None:
    """End current chat and look for a new match"""
    user_id = message.from_user.id

    if user_id not in users_data:
        await message.answer("Please register first with /start")
        return

    if user_id in active_chats:
        partner_id = active_chats[user_id]

        # Remove both users from active chats
        active_chats.pop(user_id, None)
        active_chats.pop(partner_id, None)

        # Notify both users
        await message.answer("👋 Chat ended. Looking for a new match...")
        try:
            await bot.send_message(partner_id, "👋 Your chat partner has left. Looking for a new match...")
        except Exception as e:
            logging.error(f"Error notifying partner {partner_id}: {e}")

        # Try to find new matches for both users
        match_id = await find_match(user_id)
        if match_id:
            await create_match(user_id, match_id)
        else:
            await bot.send_message(user_id, "🔍 No matches found right now. We'll notify you when someone joins!")

        partner_match_id = await find_match(partner_id)
        if partner_match_id:
            await create_match(partner_id, partner_match_id)
        else:
            try:
                await bot.send_message(partner_id, "🔍 No matches found right now. We'll notify you when someone joins!")
            except Exception as e:
                logging.error(f"Error sending no match message to {partner_id}: {e}")
    else:
        await message.answer("❌ You're not currently in a chat.")


@dp.message(F.text == "/next")
async def next_match_command(message: Message) -> None:
    """Find a new match (alias for /end)"""
    await end_chat_command(message)


@dp.message(F.text == "/status")
async def status_command(message: Message) -> None:
    """Show current chat status"""
    user_id = message.from_user.id

    if user_id not in users_data:
        await message.answer("Please register first with /start")
        return

    if user_id in active_chats:
        await message.answer(
            "💬 You are currently in a chat!\n\n"
            "Commands:\n"
            "• /end - End current chat and find new match\n"
            "• /next - Same as /end\n"
            "• /status - Show this status"
        )
    else:
        total_users = len(users_data)
        active_users = len(active_chats) // 2  # Divide by 2 since each chat has 2 entries
        available_users = total_users - len(active_chats)

        await message.answer(
            f"🔍 Looking for a match...\n\n"
            f"📊 Stats:\n"
            f"• Total users: {total_users}\n"
            f"• Active chats: {active_users}\n"
            f"• Available users: {available_users}\n\n"
            "💡 Send any message to search for a match!"
        )


async def main() -> None:
    """
    Main function to start the bot
    """
    # And the run events dispatching
    await dp.start_polling(bot)


if __name__ == "__main__":
    logging.info("Starting FlirtLine Bot...")
    asyncio.run(main())
