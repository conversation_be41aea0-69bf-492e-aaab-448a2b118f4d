import asyncio
import logging
import os
from dotenv import load_dotenv
from aiogram import <PERSON><PERSON>, Di<PERSON>atch<PERSON>, types, F
from aiogram.filters import CommandStart, StateFilter
from aiogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.fsm.storage.memory import MemoryStorage

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)

# Bot token from environment variable
BOT_TOKEN = os.getenv('BOT_TOKEN')

# User data storage (in production, use a database)
users_data = {}

# States for registration process
class RegistrationStates(StatesGroup):
    waiting_for_nickname = State()
    waiting_for_age = State()
    waiting_for_gender = State()
    waiting_for_preferred_gender = State()
    waiting_for_language = State()

# Initialize bot and dispatcher with memory storage for FSM
storage = MemoryStorage()
bot = Bot(token=BOT_TOKEN)
dp = Dispatcher(storage=storage)


@dp.message(CommandStart())
async def command_start_handler(message: Message, state: FSMContext) -> None:
    """
    This handler receives messages with `/start` command and starts registration
    """
    user_id = message.from_user.id

    # Check if user is already registered
    if user_id in users_data:
        await message.answer(
            f"👋 Welcome back, {users_data[user_id]['nickname']}!\n\n"
            "You're already registered. Your profile:\n"
            f"📝 Nickname: {users_data[user_id]['nickname']}\n"
            f"🎂 Age: {users_data[user_id]['age']}\n"
            f"👤 Gender: {users_data[user_id]['gender']}\n"
            f"💕 Looking for: {users_data[user_id]['looking_for']}\n"
            f"🌍 Language: {users_data[user_id]['language']}"
        )
        return

    welcome_text = (
        f"👋 Hello, {message.from_user.full_name}!\n\n"
        "Welcome to FlirtLine Bot! 💕\n"
        "I'm here to help you with your conversations.\n\n"
        "Let's set up your profile step by step.\n"
        "First, please tell me your nickname (not your Telegram username):"
    )

    await message.answer(welcome_text)
    await state.set_state(RegistrationStates.waiting_for_nickname)


# Registration step handlers
@dp.message(StateFilter(RegistrationStates.waiting_for_nickname))
async def process_nickname(message: Message, state: FSMContext) -> None:
    """Handle nickname input"""
    nickname = message.text.strip()

    if len(nickname) < 2 or len(nickname) > 20:
        await message.answer("Please enter a nickname between 2 and 20 characters:")
        return

    await state.update_data(nickname=nickname)
    await message.answer(f"Great! Nice to meet you, {nickname}! 😊\n\nNow, please tell me your age:")
    await state.set_state(RegistrationStates.waiting_for_age)


@dp.message(StateFilter(RegistrationStates.waiting_for_age))
async def process_age(message: Message, state: FSMContext) -> None:
    """Handle age input"""
    try:
        age = int(message.text.strip())
        if age < 18 or age > 100:
            await message.answer("Please enter a valid age between 18 and 100:")
            return
    except ValueError:
        await message.answer("Please enter a valid number for your age:")
        return

    await state.update_data(age=age)

    # Create gender selection keyboard
    gender_keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="👨 Male", callback_data="gender_male")],
        [InlineKeyboardButton(text="👩 Female", callback_data="gender_female")]
    ])

    await message.answer("Perfect! Now, please select your gender:", reply_markup=gender_keyboard)
    await state.set_state(RegistrationStates.waiting_for_gender)


@dp.callback_query(StateFilter(RegistrationStates.waiting_for_gender), F.data.startswith("gender_"))
async def process_gender(callback: CallbackQuery, state: FSMContext) -> None:
    """Handle gender selection"""
    gender = "Male" if callback.data == "gender_male" else "Female"
    await state.update_data(gender=gender)

    # Create preferred gender selection keyboard
    preferred_keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="👨 Male", callback_data="preferred_male")],
        [InlineKeyboardButton(text="👩 Female", callback_data="preferred_female")]
    ])

    await callback.message.edit_text(
        f"Got it! You selected: {gender} 👍\n\n"
        "Now, what gender would you prefer to chat with?",
        reply_markup=preferred_keyboard
    )
    await state.set_state(RegistrationStates.waiting_for_preferred_gender)
    await callback.answer()


@dp.callback_query(StateFilter(RegistrationStates.waiting_for_preferred_gender), F.data.startswith("preferred_"))
async def process_preferred_gender(callback: CallbackQuery, state: FSMContext) -> None:
    """Handle preferred gender selection"""
    looking_for = "Male" if callback.data == "preferred_male" else "Female"
    await state.update_data(looking_for=looking_for)

    # Create language selection keyboard
    language_keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="🇺🇸 English", callback_data="lang_english")],
        [InlineKeyboardButton(text="🇪🇸 Spanish", callback_data="lang_spanish")],
        [InlineKeyboardButton(text="🇫🇷 French", callback_data="lang_french")],
        [InlineKeyboardButton(text="🇩🇪 German", callback_data="lang_german")],
        [InlineKeyboardButton(text="🇮🇹 Italian", callback_data="lang_italian")]
    ])

    await callback.message.edit_text(
        f"Excellent! You prefer to chat with: {looking_for} 💕\n\n"
        "Finally, please select your preferred language:",
        reply_markup=language_keyboard
    )
    await state.set_state(RegistrationStates.waiting_for_language)
    await callback.answer()


@dp.callback_query(StateFilter(RegistrationStates.waiting_for_language), F.data.startswith("lang_"))
async def process_language(callback: CallbackQuery, state: FSMContext) -> None:
    """Handle language selection and complete registration"""
    language_map = {
        "lang_english": "English",
        "lang_spanish": "Spanish",
        "lang_french": "French",
        "lang_german": "German",
        "lang_italian": "Italian"
    }

    language = language_map.get(callback.data, "English")
    await state.update_data(language=language)

    # Get all collected data
    data = await state.get_data()
    user_id = callback.from_user.id

    # Save to users_data dictionary
    users_data[user_id] = {
        "nickname": data['nickname'],
        "age": data['age'],
        "gender": data['gender'],
        "looking_for": data['looking_for'],
        "language": data['language']
    }

    # Send completion message
    completion_text = (
        f"🎉 Registration complete! Welcome to FlirtLine, {data['nickname']}!\n\n"
        "📋 Your profile summary:\n"
        f"📝 Nickname: {data['nickname']}\n"
        f"🎂 Age: {data['age']}\n"
        f"👤 Gender: {data['gender']}\n"
        f"💕 Looking for: {data['looking_for']}\n"
        f"🌍 Language: {language}\n\n"
        "You're all set! You can now start using FlirtLine Bot. 💕"
    )

    await callback.message.edit_text(completion_text)
    await state.clear()
    await callback.answer("Registration completed! 🎉")


@dp.message()
async def echo_handler(message: Message) -> None:
    """
    Default handler for unregistered users or general messages
    """
    user_id = message.from_user.id

    if user_id not in users_data:
        await message.answer(
            "Please start by typing /start to register your profile first! 😊"
        )
    else:
        await message.answer(
            f"Hello {users_data[user_id]['nickname']}! 👋\n"
            "Your message has been received. More features coming soon! 🚀"
        )


async def main() -> None:
    """
    Main function to start the bot
    """
    # And the run events dispatching
    await dp.start_polling(bot)


if __name__ == "__main__":
    logging.info("Starting FlirtLine Bot...")
    asyncio.run(main())
