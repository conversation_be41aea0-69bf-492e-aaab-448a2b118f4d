import asyncio
import logging
import os
from dotenv import load_dotenv
from aiogram import <PERSON><PERSON>, <PERSON><PERSON>atch<PERSON>, types
from aiogram.filters import CommandStart
from aiogram.types import Message

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)

# Bot token from environment variable
BOT_TOKEN = os.getenv('BOT_TOKEN')

# Initialize bot and dispatcher
bot = Bot(token=BOT_TOKEN)
dp = Dispatcher()


@dp.message(CommandStart())
async def command_start_handler(message: Message) -> None:
    """
    This handler receives messages with `/start` command
    """
    welcome_text = (
        f"👋 Hello, {message.from_user.full_name}!\n\n"
        "Welcome to FlirtLine Bot! 💕\n"
        "I'm here to help you with your conversations.\n\n"
        "Type /help to see available commands."
    )
    
    await message.answer(welcome_text)


@dp.message()
async def echo_handler(message: Message) -> None:
    """
    Handler will forward received message back to the sender
    """
    try:
        # Send a copy of the received message
        await message.send_copy(chat_id=message.chat.id)
    except TypeError:
        # But not all the types is supported to be copied so need to handle it
        await message.answer("Nice try!")


async def main() -> None:
    """
    Main function to start the bot
    """
    # And the run events dispatching
    await dp.start_polling(bot)


if __name__ == "__main__":
    logging.info("Starting FlirtLine Bot...")
    asyncio.run(main())
