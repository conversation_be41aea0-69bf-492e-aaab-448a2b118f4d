"""
Test script to demonstrate the three new features:
1. Enhanced /end command with specific messages
2. /search command for finding new matches
3. Nickname prepending to all forwarded messages
"""

# Simulate the bot's data structures
users_data = {}
active_chats = {}

def register_user(user_id, nickname, age, gender, looking_for, language):
    """Register a user with exact bot structure"""
    users_data[user_id] = {
        "nickname": nickname,
        "age": age,
        "gender": gender,
        "looking_for": looking_for,
        "language": language
    }
    print(f"✅ {nickname} registered successfully!")

def create_match(user1_id, user2_id):
    """Create a match between two users"""
    active_chats[user1_id] = user2_id
    active_chats[user2_id] = user1_id
    
    user1_name = users_data[user1_id]["nickname"]
    user2_name = users_data[user2_id]["nickname"]
    print(f"🎉 MATCH CREATED: {user1_name} ↔ {user2_name}")
    print(f"   Both users receive: 'You are now connected anonymously. Say hi!'")

def simulate_end_command(user_id):
    """Simulate the enhanced /end command"""
    print(f"\n🔚 {users_data[user_id]['nickname']} sends /end command")
    
    if user_id not in active_chats:
        print("❌ Response: You're not currently in a chat.")
        return
    
    partner_id = active_chats[user_id]
    user_name = users_data[user_id]["nickname"]
    partner_name = users_data[partner_id]["nickname"]
    
    # Remove both users from active chats
    active_chats.pop(user_id, None)
    active_chats.pop(partner_id, None)
    
    # Send the exact messages as implemented
    end_message = "❌ Your chat has ended. The other person has left the conversation."
    search_message = "🔍 Do you want to search for a new match? Type /search"
    
    print(f"📤 Message to {user_name}: {end_message}")
    print(f"📤 Message to {user_name}: {search_message}")
    print(f"📤 Message to {partner_name}: {end_message}")
    print(f"📤 Message to {partner_name}: {search_message}")
    
    print(f"✅ Chat ended between {user_name} and {partner_name}")

def simulate_search_command(user_id):
    """Simulate the /search command"""
    print(f"\n🔍 {users_data[user_id]['nickname']} sends /search command")
    
    if user_id in active_chats:
        print("❌ Response: You're already in a chat. Use /end to leave your current chat first.")
        return
    
    # Try to find a match (simplified matching logic)
    current_user = users_data[user_id]
    match_found = False
    
    for potential_match_id, potential_match in users_data.items():
        if potential_match_id == user_id:
            continue
        if user_id in active_chats or potential_match_id in active_chats:
            continue
        if (current_user["looking_for"] == potential_match["gender"] and 
            potential_match["looking_for"] == current_user["gender"]):
            create_match(user_id, potential_match_id)
            match_found = True
            break
    
    if not match_found:
        print("📤 Response: 🔍 No matches found right now. We'll notify you when someone compatible joins! 💕")

def simulate_message_with_nickname(sender_id, message_type, content=""):
    """Simulate message forwarding with nickname prepending"""
    sender_name = users_data[sender_id]["nickname"]
    print(f"\n💬 {sender_name} sends {message_type}: {content}")
    
    if sender_id not in active_chats:
        print("❌ Response: You're not connected to anyone yet. Please wait for a match.")
        return
    
    partner_id = active_chats[sender_id]
    partner_name = users_data[partner_id]["nickname"]
    
    # Simulate the nickname prepending as implemented in the bot
    if message_type == "text":
        forwarded_message = f"💬 [{sender_name}]: {content}"
        print(f"📤 Forwarded to {partner_name}: {forwarded_message}")
    elif message_type == "photo":
        if content:
            forwarded_caption = f"💬 [{sender_name}]: {content}"
        else:
            forwarded_caption = f"💬 [{sender_name}] sent a photo"
        print(f"📸 Forwarded photo to {partner_name} with caption: {forwarded_caption}")
    elif message_type == "voice":
        print(f"📤 Forwarded to {partner_name}: 💬 [{sender_name}] sent a voice message:")
        print(f"🎤 Voice message forwarded")
    elif message_type == "video":
        if content:
            forwarded_caption = f"💬 [{sender_name}]: {content}"
        else:
            forwarded_caption = f"💬 [{sender_name}] sent a video"
        print(f"🎥 Forwarded video to {partner_name} with caption: {forwarded_caption}")
    elif message_type == "sticker":
        print(f"📤 Forwarded to {partner_name}: 💬 [{sender_name}] sent a sticker:")
        print(f"😀 Sticker forwarded")
    elif message_type == "document":
        if content:
            forwarded_caption = f"💬 [{sender_name}]: {content}"
        else:
            forwarded_caption = f"💬 [{sender_name}] sent a document"
        print(f"📄 Forwarded document to {partner_name} with caption: {forwarded_caption}")
    else:
        print(f"📤 Forwarded to {partner_name}: 💬 [{sender_name}] sent a message:")
        print(f"📤 {message_type} forwarded")

def demonstrate_new_features():
    print("🎯 FlirtLine Bot - New Features Demonstration")
    print("=" * 60)
    
    # Setup: Register users
    print("\n📝 SETUP: Registering Users")
    print("-" * 30)
    register_user(101, "Alice", 25, "Female", "Male", "English")
    register_user(102, "Bob", 28, "Male", "Female", "English")
    register_user(103, "Charlie", 22, "Male", "Male", "English")
    
    # Feature 1: Test message forwarding with nicknames
    print("\n🎯 FEATURE 1: Message Forwarding with Nicknames")
    print("-" * 50)
    
    # Create a match first
    create_match(101, 102)
    
    # Test different message types with nickname prepending
    simulate_message_with_nickname(101, "text", "Hi Bob! How are you? 😊")
    simulate_message_with_nickname(102, "text", "Hello Alice! I'm doing great!")
    simulate_message_with_nickname(101, "photo", "Beautiful sunset today!")
    simulate_message_with_nickname(102, "photo", "")  # Photo without caption
    simulate_message_with_nickname(101, "voice", "")
    simulate_message_with_nickname(102, "video", "Funny cat video 😸")
    simulate_message_with_nickname(101, "sticker", "")
    simulate_message_with_nickname(102, "document", "Here's that file you wanted")
    
    # Feature 2: Test enhanced /end command
    print("\n🎯 FEATURE 2: Enhanced /end Command")
    print("-" * 40)
    
    simulate_end_command(101)  # Alice ends the chat
    
    print(f"\nActive chats after /end: {dict(active_chats)}")
    
    # Feature 3: Test /search command
    print("\n🎯 FEATURE 3: /search Command")
    print("-" * 30)
    
    # Alice tries to search for a new match
    simulate_search_command(101)
    
    # Bob tries to search for a new match
    simulate_search_command(102)
    
    # Charlie tries to search (should find no match)
    simulate_search_command(103)
    
    # Test /search when already in a chat
    if active_chats:
        user_in_chat = list(active_chats.keys())[0]
        print(f"\n🔒 Testing /search while in chat:")
        simulate_search_command(user_in_chat)
    
    # Final status
    print(f"\n📊 FINAL STATUS")
    print("-" * 20)
    print(f"Total users: {len(users_data)}")
    print(f"Active chats: {len(active_chats) // 2}")
    print(f"Active chats structure: {dict(active_chats)}")
    
    print(f"\n✨ NEW FEATURES DEMONSTRATION COMPLETE!")
    print(f"\n🎯 Features Demonstrated:")
    print(f"✅ Enhanced /end command with exact messages:")
    print(f"   • '❌ Your chat has ended. The other person has left the conversation.'")
    print(f"   • '🔍 Do you want to search for a new match? Type /search'")
    print(f"✅ /search command for finding new matches")
    print(f"✅ Nickname prepending to ALL message types:")
    print(f"   • Text: '💬 [nickname]: message'")
    print(f"   • Media: '💬 [nickname]: caption' or '💬 [nickname] sent a [type]'")
    print(f"   • Voice/Sticker: '💬 [nickname] sent a [type]:' + forwarded content")

if __name__ == "__main__":
    demonstrate_new_features()
