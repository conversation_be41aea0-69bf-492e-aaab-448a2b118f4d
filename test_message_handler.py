"""
Test script to demonstrate the enhanced message handler functionality
Shows how different message types are handled for users in and out of chats
"""

# Simulate the bot's data structures
users_data = {}
active_chats = {}

class MessageType:
    """Simulate different message types"""
    def __init__(self, msg_type, content=None, file_id=None, caption=None):
        self.type = msg_type
        self.content = content
        self.file_id = file_id
        self.caption = caption
        
        # Simulate message properties
        self.text = content if msg_type == "text" else None
        self.photo = [{"file_id": file_id}] if msg_type == "photo" else None
        self.voice = {"file_id": file_id} if msg_type == "voice" else None
        self.video = {"file_id": file_id} if msg_type == "video" else None
        self.document = {"file_id": file_id} if msg_type == "document" else None
        self.sticker = {"file_id": file_id} if msg_type == "sticker" else None
        self.audio = {"file_id": file_id} if msg_type == "audio" else None
        self.animation = {"file_id": file_id} if msg_type == "animation" else None

def register_user(user_id, nickname, age, gender, looking_for, language):
    """Register a user"""
    users_data[user_id] = {
        "nickname": nickname,
        "age": age,
        "gender": gender,
        "looking_for": looking_for,
        "language": language
    }
    print(f"✅ {nickname} registered successfully!")

def create_match(user1_id, user2_id):
    """Create a match between two users"""
    active_chats[user1_id] = user2_id
    active_chats[user2_id] = user1_id
    
    user1_name = users_data[user1_id]["nickname"]
    user2_name = users_data[user2_id]["nickname"]
    print(f"🎉 {user1_name} and {user2_name} are now matched!")

def simulate_message_handler(user_id, message):
    """Simulate the enhanced message handler logic"""
    print(f"\n📨 Processing message from User {user_id}")
    
    # Check if user is registered
    if user_id not in users_data:
        print("❌ Response: Please start by typing /start to register your profile first! 😊")
        return
    
    sender_name = users_data[user_id]["nickname"]
    
    # Check if user is in an active chat
    if user_id in active_chats:
        partner_id = active_chats[user_id]
        partner_name = users_data[partner_id]["nickname"]
        
        # Simulate forwarding based on message type
        if message.type == "text":
            print(f"💬 Forwarding text to {partner_name}: '{message.content}'")
        elif message.type == "photo":
            caption_text = f" (Caption: {message.caption})" if message.caption else ""
            print(f"📸 Forwarding photo to {partner_name}{caption_text}")
        elif message.type == "voice":
            print(f"🎤 Forwarding voice message to {partner_name}")
        elif message.type == "video":
            caption_text = f" (Caption: {message.caption})" if message.caption else ""
            print(f"🎥 Forwarding video to {partner_name}{caption_text}")
        elif message.type == "document":
            caption_text = f" (Caption: {message.caption})" if message.caption else ""
            print(f"📄 Forwarding document to {partner_name}{caption_text}")
        elif message.type == "sticker":
            print(f"😀 Forwarding sticker to {partner_name}")
        elif message.type == "audio":
            caption_text = f" (Caption: {message.caption})" if message.caption else ""
            print(f"🎵 Forwarding audio to {partner_name}{caption_text}")
        elif message.type == "animation":
            caption_text = f" (Caption: {message.caption})" if message.caption else ""
            print(f"🎬 Forwarding animation/GIF to {partner_name}{caption_text}")
        else:
            print(f"📤 Forwarding {message.type} to {partner_name}")
            
        print(f"✅ Message successfully forwarded from {sender_name} to {partner_name}")
    else:
        # User is not in a chat - exact message as requested
        print(f"❌ Response to {sender_name}: You're not connected to anyone yet. Please wait for a match.")

def test_message_types():
    """Test various message types"""
    print("🧪 TESTING MESSAGE HANDLER FOR ALL MESSAGE TYPES")
    print("=" * 60)
    
    # Register users
    register_user(101, "Alice", 25, "Female", "Male", "English")
    register_user(102, "Bob", 28, "Male", "Female", "English")
    register_user(103, "Charlie", 22, "Male", "Male", "English")
    
    # Create a match between Alice and Bob
    create_match(101, 102)
    
    print(f"\n📊 Current Status:")
    print(f"   Active chats: {dict(active_chats)}")
    
    # Test messages from matched user (Alice)
    print(f"\n🔗 TESTING MESSAGES FROM MATCHED USER (Alice)")
    print("-" * 40)
    
    test_messages = [
        MessageType("text", "Hello Bob! How are you? 😊"),
        MessageType("photo", file_id="photo123", caption="Check out this sunset!"),
        MessageType("voice", file_id="voice456"),
        MessageType("video", file_id="video789", caption="Funny cat video"),
        MessageType("document", file_id="doc101", caption="Here's that document"),
        MessageType("sticker", file_id="sticker202"),
        MessageType("audio", file_id="audio303", caption="My favorite song"),
        MessageType("animation", file_id="gif404", caption="LOL this is hilarious"),
    ]
    
    for msg in test_messages:
        simulate_message_handler(101, msg)  # Alice sending messages
    
    # Test messages from unmatched user (Charlie)
    print(f"\n❌ TESTING MESSAGES FROM UNMATCHED USER (Charlie)")
    print("-" * 40)
    
    for msg in test_messages[:3]:  # Test a few message types
        simulate_message_handler(103, msg)  # Charlie sending messages
    
    # Test unregistered user
    print(f"\n🚫 TESTING MESSAGES FROM UNREGISTERED USER")
    print("-" * 40)
    
    simulate_message_handler(999, MessageType("text", "Hello?"))

def test_chat_scenarios():
    """Test different chat scenarios"""
    print(f"\n\n🎭 TESTING DIFFERENT CHAT SCENARIOS")
    print("=" * 60)
    
    # Clear previous data
    users_data.clear()
    active_chats.clear()
    
    # Scenario 1: User sends message before registration
    print(f"\n📝 SCENARIO 1: Unregistered user sends message")
    print("-" * 40)
    simulate_message_handler(201, MessageType("text", "Hi there!"))
    
    # Scenario 2: Registered user with no match
    print(f"\n📝 SCENARIO 2: Registered user with no match")
    print("-" * 40)
    register_user(201, "Diana", 24, "Female", "Female", "English")
    simulate_message_handler(201, MessageType("text", "Looking for someone to chat with"))
    simulate_message_handler(201, MessageType("photo", file_id="photo555", caption="My profile pic"))
    
    # Scenario 3: Two users get matched and exchange messages
    print(f"\n📝 SCENARIO 3: Matched users exchanging messages")
    print("-" * 40)
    register_user(202, "Eve", 26, "Female", "Female", "Spanish")
    create_match(201, 202)
    
    simulate_message_handler(201, MessageType("text", "Hi! Nice to meet you! 👋"))
    simulate_message_handler(202, MessageType("text", "Hello! How are you doing? 😊"))
    simulate_message_handler(201, MessageType("sticker", file_id="happy_sticker"))
    simulate_message_handler(202, MessageType("voice", file_id="voice_response"))

if __name__ == "__main__":
    print("🎯 FlirtLine Bot - Enhanced Message Handler Test")
    print("=" * 60)
    
    test_message_types()
    test_chat_scenarios()
    
    print(f"\n✨ All message handler tests completed!")
    print(f"\nKey Features Demonstrated:")
    print(f"✅ Handles all message types (text, photo, voice, video, document, sticker, audio, animation)")
    print(f"✅ Forwards messages between matched users")
    print(f"✅ Provides exact response for unmatched users: 'You're not connected to anyone yet. Please wait for a match.'")
    print(f"✅ Handles unregistered users appropriately")
    print(f"✅ Preserves captions for media messages")
