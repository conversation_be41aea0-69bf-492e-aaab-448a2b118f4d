"""
Demonstration of the exact users_data structure as requested
"""

# Global Python dictionary called "users_data"
users_data = {}

def add_user_example():
    """Add example users with the exact structure requested"""
    
    # Example user 1
    user_id_1 = 123456789
    users_data[user_id_1] = {
        "nickname": "<PERSON>",
        "age": 25,
        "gender": "Female", 
        "looking_for": "Male",
        "language": "English"
    }
    
    # Example user 2
    user_id_2 = 987654321
    users_data[user_id_2] = {
        "nickname": "Bob",
        "age": 28,
        "gender": "Male",
        "looking_for": "Female", 
        "language": "Spanish"
    }
    
    # Example user 3
    user_id_3 = 555666777
    users_data[user_id_3] = {
        "nickname": "<PERSON>",
        "age": 22,
        "gender": "Male",
        "looking_for": "Male",
        "language": "French"
    }

def print_structure():
    """Print the exact structure"""
    print("📋 users_data structure:")
    print("users_data = {")
    for user_id, data in users_data.items():
        print(f"    {user_id}: {{")
        print(f'        "nickname": "{data["nickname"]}",')
        print(f'        "age": {data["age"]},')
        print(f'        "gender": "{data["gender"]}",')
        print(f'        "looking_for": "{data["looking_for"]}",')
        print(f'        "language": "{data["language"]}"')
        print("    },")
    print("}")

def access_user_data(user_id):
    """Demonstrate how to access user data"""
    if user_id in users_data:
        user = users_data[user_id]
        print(f"\n🔍 Accessing data for user {user_id}:")
        print(f"users_data[{user_id}]['nickname'] = '{user['nickname']}'")
        print(f"users_data[{user_id}]['age'] = {user['age']}")
        print(f"users_data[{user_id}]['gender'] = '{user['gender']}'")
        print(f"users_data[{user_id}]['looking_for'] = '{user['looking_for']}'")
        print(f"users_data[{user_id}]['language'] = '{user['language']}'")
    else:
        print(f"❌ User {user_id} not found")

if __name__ == "__main__":
    print("🎯 Exact users_data Structure Demo\n")
    
    # Add example users
    add_user_example()
    
    # Print the structure
    print_structure()
    
    # Demonstrate access
    access_user_data(123456789)
    access_user_data(987654321)
    
    print(f"\n✅ Total users in users_data: {len(users_data)}")
    print("🎉 Structure demonstration complete!")
