"""
Complete demonstration of the FlirtLine Bot system including:
- User registration
- Matching algorithm
- Active chat management
- Message forwarding simulation
"""

# Global dictionaries as used in the actual bot
users_data = {}
active_chats = {}

class FlirtLineDemo:
    def __init__(self):
        self.message_log = []
    
    def register_user(self, user_id, nickname, age, gender, looking_for, language):
        """Register a new user with the exact structure used by the bot"""
        users_data[user_id] = {
            "nickname": nickname,
            "age": age,
            "gender": gender,
            "looking_for": looking_for,
            "language": language
        }
        print(f"✅ {nickname} registered successfully!")
        
        # Try to find a match immediately (as the bot does)
        match_id = self.find_match(user_id)
        if match_id:
            self.create_match(user_id, match_id)
        else:
            print(f"🔍 No matches found for {nickname} right now.")
    
    def find_match(self, user_id):
        """Find a compatible match using the exact algorithm from the bot"""
        if user_id not in users_data:
            return None
        
        current_user = users_data[user_id]
        
        for potential_match_id, potential_match in users_data.items():
            # Skip self
            if potential_match_id == user_id:
                continue
                
            # Skip if either user is already chatting
            if user_id in active_chats or potential_match_id in active_chats:
                continue
                
            # Check if preferences match (mutual compatibility)
            if (current_user["looking_for"] == potential_match["gender"] and 
                potential_match["looking_for"] == current_user["gender"]):
                return potential_match_id
        
        return None
    
    def create_match(self, user1_id, user2_id):
        """Create a match using the exact structure from the bot"""
        # Store the match in active_chats
        active_chats[user1_id] = user2_id
        active_chats[user2_id] = user1_id
        
        user1_name = users_data[user1_id]["nickname"]
        user2_name = users_data[user2_id]["nickname"]
        
        print(f"🎉 MATCH CREATED!")
        print(f"   {user1_name} ↔ {user2_name}")
        print(f"   Both users receive: 'You are now connected anonymously. Say hi!'")
    
    def send_message(self, from_user_id, message_text):
        """Simulate sending a message (as the bot would forward it)"""
        if from_user_id not in users_data:
            print(f"❌ User {from_user_id} not registered")
            return
        
        sender_name = users_data[from_user_id]["nickname"]
        
        if from_user_id in active_chats:
            partner_id = active_chats[from_user_id]
            partner_name = users_data[partner_id]["nickname"]
            
            print(f"💬 {sender_name} → {partner_name}: '{message_text}'")
            self.message_log.append(f"{sender_name} → {partner_name}: {message_text}")
        else:
            print(f"❌ {sender_name} is not in a chat. Looking for match...")
            match_id = self.find_match(from_user_id)
            if match_id:
                self.create_match(from_user_id, match_id)
            else:
                print(f"🔍 No matches found for {sender_name}")
    
    def end_chat(self, user_id):
        """End a chat (as the /end command does)"""
        if user_id not in active_chats:
            user_name = users_data[user_id]["nickname"]
            print(f"❌ {user_name} is not in a chat")
            return
        
        partner_id = active_chats[user_id]
        user_name = users_data[user_id]["nickname"]
        partner_name = users_data[partner_id]["nickname"]
        
        # Remove both users from active chats
        active_chats.pop(user_id, None)
        active_chats.pop(partner_id, None)
        
        print(f"👋 {user_name} ended chat with {partner_name}")
        
        # Try to find new matches for both users
        match_id = self.find_match(user_id)
        if match_id:
            self.create_match(user_id, match_id)
        
        partner_match_id = self.find_match(partner_id)
        if partner_match_id:
            self.create_match(partner_id, partner_match_id)
    
    def show_system_status(self):
        """Show current system status (as /status command does)"""
        total_users = len(users_data)
        active_chats_count = len(active_chats) // 2
        available_users = total_users - len(active_chats)
        
        print(f"\n📊 System Status:")
        print(f"   Total users: {total_users}")
        print(f"   Active chats: {active_chats_count}")
        print(f"   Available users: {available_users}")
        
        if active_chats:
            print(f"\n💬 Active Chats:")
            processed = set()
            for user_id, partner_id in active_chats.items():
                if user_id not in processed:
                    user1_name = users_data[user_id]["nickname"]
                    user2_name = users_data[partner_id]["nickname"]
                    print(f"   {user1_name} ↔ {user2_name}")
                    processed.add(user_id)
                    processed.add(partner_id)

def main():
    print("🎯 FlirtLine Bot - Complete System Demo")
    print("=" * 50)
    
    demo = FlirtLineDemo()
    
    # Scenario 1: Basic matching
    print("\n📝 SCENARIO 1: Basic Registration and Matching")
    print("-" * 40)
    demo.register_user(101, "Alice", 25, "Female", "Male", "English")
    demo.register_user(102, "Bob", 28, "Male", "Female", "English")
    demo.show_system_status()
    
    # Scenario 2: Message exchange
    print("\n💬 SCENARIO 2: Message Exchange")
    print("-" * 40)
    demo.send_message(101, "Hi there! 👋")
    demo.send_message(102, "Hello! How are you? 😊")
    demo.send_message(101, "I'm great! What do you like to do for fun?")
    
    # Scenario 3: More users joining
    print("\n👥 SCENARIO 3: More Users Joining")
    print("-" * 40)
    demo.register_user(103, "Charlie", 22, "Male", "Male", "English")
    demo.register_user(104, "Diana", 24, "Female", "Female", "English")
    demo.show_system_status()
    
    # Scenario 4: Chat ending and rematching
    print("\n🔄 SCENARIO 4: Chat Ending and Rematching")
    print("-" * 40)
    demo.end_chat(101)  # Alice ends chat with Bob
    demo.show_system_status()
    
    # Scenario 5: New user gets matched with available user
    print("\n🆕 SCENARIO 5: New User Matching")
    print("-" * 40)
    demo.register_user(105, "Eve", 26, "Female", "Male", "Spanish")
    demo.show_system_status()
    
    print("\n📋 Message Log:")
    for msg in demo.message_log:
        print(f"   {msg}")
    
    print(f"\n✨ Demo completed!")
    print(f"\nFinal active_chats structure:")
    print(f"active_chats = {active_chats}")

if __name__ == "__main__":
    main()
