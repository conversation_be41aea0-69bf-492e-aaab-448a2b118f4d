"""
Simple test script to demonstrate the user data storage functionality
"""

# Simulate the users_data dictionary that would be populated by the bot
users_data = {}

def simulate_registration(user_id, nickname, age, gender, looking_for, language):
    """Simulate a user registration"""
    users_data[user_id] = {
        "nickname": nickname,
        "age": age,
        "gender": gender,
        "looking_for": looking_for,
        "language": language
    }
    print(f"✅ User {user_id} registered successfully!")

def display_user_profile(user_id):
    """Display a user's profile"""
    if user_id in users_data:
        profile = users_data[user_id]
        print(f"\n👤 Profile for User ID: {user_id}")
        print(f"📝 Nickname: {profile['nickname']}")
        print(f"🎂 Age: {profile['age']}")
        print(f"👤 Gender: {profile['gender']}")
        print(f"💕 Looking for: {profile['looking_for']}")
        print(f"🌍 Language: {profile['language']}")
    else:
        print(f"❌ User {user_id} not found in database")

def list_all_users():
    """List all registered users"""
    print(f"\n📊 Total registered users: {len(users_data)}")
    for user_id, profile in users_data.items():
        print(f"User {user_id}: {profile['nickname']} ({profile['age']}, {profile['gender']})")

if __name__ == "__main__":
    print("🤖 FlirtLine Bot - User Data Storage Test\n")
    
    # Simulate some user registrations
    simulate_registration(123456789, "Alice", 25, "Female", "Male", "English")
    simulate_registration(987654321, "Bob", 28, "Male", "Female", "Spanish")
    simulate_registration(555666777, "Charlie", 22, "Male", "Male", "French")
    
    # Display profiles
    display_user_profile(123456789)
    display_user_profile(987654321)
    display_user_profile(555666777)
    display_user_profile(999999999)  # Non-existent user
    
    # List all users
    list_all_users()
    
    print("\n✨ Test completed!")
