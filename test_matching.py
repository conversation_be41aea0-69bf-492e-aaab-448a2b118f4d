"""
Test script to demonstrate the matching system functionality
"""

# Simulate the dictionaries used by the bot
users_data = {}
active_chats = {}

def register_user(user_id, nickname, age, gender, looking_for, language):
    """Register a new user"""
    users_data[user_id] = {
        "nickname": nickname,
        "age": age,
        "gender": gender,
        "looking_for": looking_for,
        "language": language
    }
    print(f"✅ {nickname} (ID: {user_id}) registered successfully!")

def find_match(user_id):
    """Find a compatible match for the given user"""
    if user_id not in users_data:
        return None
    
    current_user = users_data[user_id]
    
    # Look for available users (not currently chatting)
    for potential_match_id, potential_match in users_data.items():
        # Skip self
        if potential_match_id == user_id:
            continue
            
        # Skip if either user is already chatting
        if user_id in active_chats or potential_match_id in active_chats:
            continue
            
        # Check if preferences match
        if (current_user["looking_for"] == potential_match["gender"] and 
            potential_match["looking_for"] == current_user["gender"]):
            return potential_match_id
    
    return None

def create_match(user1_id, user2_id):
    """Create a match between two users"""
    active_chats[user1_id] = user2_id
    active_chats[user2_id] = user1_id
    
    user1_name = users_data[user1_id]["nickname"]
    user2_name = users_data[user2_id]["nickname"]
    
    print(f"🎉 MATCH FOUND! {user1_name} and {user2_name} are now connected!")
    print(f"   Both users would receive: 'You are now connected anonymously. Say hi!'")

def try_match_user(user_id):
    """Try to find and create a match for a user"""
    user_name = users_data[user_id]["nickname"]
    print(f"\n🔍 Looking for a match for {user_name}...")
    
    match_id = find_match(user_id)
    if match_id:
        create_match(user_id, match_id)
        return True
    else:
        print(f"❌ No compatible matches found for {user_name}")
        return False

def show_status():
    """Show current system status"""
    total_users = len(users_data)
    active_chats_count = len(active_chats) // 2  # Divide by 2 since each chat has 2 entries
    available_users = total_users - len(active_chats)
    
    print(f"\n📊 System Status:")
    print(f"   Total registered users: {total_users}")
    print(f"   Active chats: {active_chats_count}")
    print(f"   Available users: {available_users}")
    
    if active_chats:
        print(f"\n💬 Active Chats:")
        processed = set()
        for user_id, partner_id in active_chats.items():
            if user_id not in processed:
                user1_name = users_data[user_id]["nickname"]
                user2_name = users_data[partner_id]["nickname"]
                print(f"   {user1_name} ↔ {user2_name}")
                processed.add(user_id)
                processed.add(partner_id)

def end_chat(user_id):
    """End a chat for a user"""
    if user_id in active_chats:
        partner_id = active_chats[user_id]
        user_name = users_data[user_id]["nickname"]
        partner_name = users_data[partner_id]["nickname"]
        
        # Remove both users from active chats
        active_chats.pop(user_id, None)
        active_chats.pop(partner_id, None)
        
        print(f"👋 {user_name} ended chat with {partner_name}")
        return partner_id
    return None

if __name__ == "__main__":
    print("🤖 FlirtLine Bot - Matching System Test\n")
    
    # Register some users
    register_user(111, "Alice", 25, "Female", "Male", "English")
    register_user(222, "Bob", 28, "Male", "Female", "English")
    register_user(333, "Charlie", 22, "Male", "Male", "English")
    register_user(444, "Diana", 24, "Female", "Female", "English")
    register_user(555, "Eve", 26, "Female", "Male", "Spanish")
    
    show_status()
    
    # Test matching
    print("\n" + "="*50)
    print("TESTING MATCHING SYSTEM")
    print("="*50)
    
    # Alice should match with Bob (Female looking for Male, Male looking for Female)
    try_match_user(111)  # Alice
    show_status()
    
    # Charlie should match with Diana (Male looking for Male, Female looking for Female)
    try_match_user(333)  # Charlie
    show_status()
    
    # Eve should have no match (Alice and Bob are already chatting)
    try_match_user(555)  # Eve
    show_status()
    
    # Test ending a chat
    print("\n" + "="*50)
    print("TESTING CHAT ENDING")
    print("="*50)
    
    partner_id = end_chat(111)  # Alice ends chat
    show_status()
    
    # Now Eve should be able to match with Bob
    try_match_user(555)  # Eve
    show_status()
    
    print("\n✨ Matching system test completed!")
