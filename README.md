# FlirtLine Telegram Bot

A Telegram bot built with Python and aiogram that helps users set up their dating profile through a step-by-step registration process.

## Features

- **Step-by-step registration** after `/start` command
- Collects user information:
  - Nickname (not Telegram username)
  - Age (18-100)
  - Gender (Male/Female)
  - Preferred gender to chat with (Male/Female)
  - Language preference (English, Spanish, French, German, Italian)
- **Data storage** using Telegram user ID as key
- **Input validation** for all user inputs
- **Interactive keyboards** for gender and language selection
- **Profile summary** display after registration
- **Returning user detection** - shows existing profile if already registered
- Built with aiogram 3.x with FSM (Finite State Machine) support

## Setup

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Set up your bot token in the `.env` file (already configured)

3. Run the bot:
   ```bash
   python main.py
   ```

## Usage

1. **Start registration**: Send `/start` to begin the registration process
2. **Follow the steps**: The bot will guide you through:
   - Enter your nickname
   - Enter your age (18-100)
   - Select your gender using inline buttons
   - Select your preferred gender to chat with
   - Choose your language preference
3. **Complete registration**: View your profile summary
4. **Returning users**: If already registered, `/start` will show your existing profile

## Registration Flow

```
/start → Nickname → Age → Gender → Preferred Gender → Language → Complete!
```

## Data Storage

User data is stored in memory using the following structure:
```python
users_data = {
    user_id: {
        "nickname": str,
        "age": int,
        "gender": str,
        "looking_for": str,
        "language": str
    }
}
```

## Requirements

- Python 3.7+
- aiogram 3.3.0
- python-dotenv 1.0.0
