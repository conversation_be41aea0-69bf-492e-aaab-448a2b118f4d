# FlirtLine Telegram Bot

A Telegram bot built with Python and aiogram that helps users set up their dating profile through a step-by-step registration process.

## Features

- **Step-by-step registration** after `/start` command
- Collects user information:
  - Nickname (not Telegram username)
  - Age (18-100)
  - Gender (Male/Female)
  - Preferred gender to chat with (Male/Female)
  - Language preference (English, Spanish, French, German, Italian)
- **Automatic matching system**:
  - Finds compatible users with matching preferences
  - Both users must be looking for each other's gender
  - Only matches users who are not currently chatting
  - Instant connection after registration if match found
- **Anonymous chat functionality**:
  - Messages forwarded between matched users
  - Supports text, photos, voice, video, documents, and stickers
  - Real-time communication
- **Chat management commands**:
  - `/end` or `/next` - End current chat and find new match
  - `/status` - Show current chat status and system stats
- **Data storage** using Telegram user ID as key
- **Input validation** for all user inputs
- **Interactive keyboards** for gender and language selection
- **Profile summary** display after registration
- **Returning user detection** - shows existing profile if already registered
- Built with aiogram 3.x with FSM (Finite State Machine) support

## Setup

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Set up your bot token in the `.env` file (already configured)

3. Run the bot:
   ```bash
   python main.py
   ```

## Usage

### Registration
1. **Start registration**: Send `/start` to begin the registration process
2. **Follow the steps**: The bot will guide you through:
   - Enter your nickname
   - Enter your age (18-100)
   - Select your gender using inline buttons
   - Select your preferred gender to chat with
   - Choose your language preference
3. **Complete registration**: View your profile summary and automatic match search
4. **Returning users**: If already registered, `/start` will show your existing profile

### Chatting
- **Automatic matching**: After registration, the bot automatically looks for compatible users
- **Anonymous chat**: If a match is found, both users receive "You are now connected anonymously. Say hi!"
- **Message forwarding**: All messages (text, photos, voice, etc.) are forwarded to your chat partner
- **No match found**: The bot will notify you and search again when you send messages

### Commands
- `/start` - Register or view your profile
- `/end` or `/next` - End current chat and find a new match
- `/status` - Show your current status and system statistics

## Registration Flow

```
/start → Nickname → Age → Gender → Preferred Gender → Language → Match Search → Chat!
```

## Matching System

The bot automatically matches users based on:
- **Mutual compatibility**: Both users must be looking for each other's gender
- **Availability**: Only matches users who are not currently in a chat
- **Real-time**: Searches for matches after registration and when sending messages

## Data Storage

User data is stored in memory using the following structures:

### User Profiles
```python
users_data = {
    user_id: {
        "nickname": str,
        "age": int,
        "gender": str,
        "looking_for": str,
        "language": str
    }
}
```

### Active Chats
```python
active_chats = {
    user1_id: user2_id,
    user2_id: user1_id
}
```

When two users are matched, both entries are created in `active_chats` to enable bidirectional message forwarding.

## Requirements

- Python 3.7+
- aiogram 3.3.0
- python-dotenv 1.0.0
