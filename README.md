# FlirtLine Telegram Bot

A basic Telegram bot built with Python and aiogram that responds to the `/start` command with a welcome message.

## Features

- Responds to `/start` command with a personalized welcome message
- Echo functionality for other messages
- Built with aiogram 3.x for modern async/await support

## Setup

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Set up your bot token in the `.env` file (already configured)

3. Run the bot:
   ```bash
   python main.py
   ```

## Usage

- Send `/start` to the bot to receive a welcome message
- Send any other message and the bot will echo it back

## Requirements

- Python 3.7+
- aiogram 3.3.0
- python-dotenv 1.0.0
